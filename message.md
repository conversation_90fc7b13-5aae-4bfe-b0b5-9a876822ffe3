Refactored main-thread execution and concurrency handling.

This update focuses on modernizing the app's concurrency model by optimizing the use of `@MainActor` and `DispatchQueue.main.async`, and by refactoring the `EngineManager` to prevent UI blocking.

### Key Changes

1.  **Removed Redundant `DispatchQueue.main.async` Calls**:
    *   In `ChessGameViewModel.swift`, `ChessView.swift`, and `InteractiveMoveNotationView.swift`, numerous `DispatchQueue.main.async` wrappers were removed.
    *   **Reasoning**: The `ChessGameViewModel` is already annotated with `@MainActor`, making explicit dispatches to the main queue unnecessary. For SwiftUI views, event handlers and view updates are already executed on the main thread by default. This cleanup makes the code more concise and readable.

2.  **Refactored `EngineManager` for Improved Concurrency**:
    *   Removed the `@MainActor` annotation from the `EngineManager` class definition. This allows the engine's processing to run on a background thread, preventing it from blocking the UI.
    *   Wrapped all modifications to `@Published` properties within `EngineManager` in `await MainActor.run { ... }` blocks. This ensures that any UI-related state updates are safely dispatched back to the main thread.
    *   Updated the call site in `ChessGameViewModel.swift` (`onGameStateChanged`) to invoke `engineManager.analyzePosition` within a standard `Task`, simplifying the asynchronous interaction between the view model and the service.

These changes result in a cleaner, more modern codebase that adheres to current Swift concurrency best practices and improves the overall responsiveness of the application.
